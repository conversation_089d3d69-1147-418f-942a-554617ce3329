<?php

namespace App\Http\Controllers\Dashboard;

use App\Models\PortfolioFieldDefinition;
use App\Models\TradeFieldDefinition;
use App\Models\TransactionFieldDefinition;
use Illuminate\Http\Request;
use App\Services\TradeBuilderService;
use App\Services\FormulaEvaluatorService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Trade;
use App\Models\TradeForm;
use App\Models\TradeFormSection;
use App\Models\TradeSnapshot;

class TradeBuilderController extends Controller
{
    public function __construct(protected TradeBuilderService $tradeBuilderService, protected FormulaEvaluatorService $evaluator)
    {
    }

    /**
     * fetch a trade default entry and exit form fields.
     */
    public function index(): JsonResponse
    {
        return response()->json(
            $this->tradeBuilderService->getEntryAndExitFields()
        );
    }
    public function initialize(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|in:entry,exit',
            'index' => 'required|integer|min:1',
            'trade_id' => 'nullable|integer|exists:trades,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();

        $trade = null;
        if ($request->has('trade_id')) {
            $trade = Trade::where('id', $request->trade_id)
                ->where('user_id', $user->id)
                ->where('is_published', false)
                ->first();
        }

        if (!$trade) {
            $trade = Trade::create([
                'user_id' => $user->id,
                'is_published' => false,
                'draft_title' => 'Draft ' . (Trade::where('user_id', $user->id)->where('is_published', false)->count() + 1),
            ]);
        }

        $form = TradeForm::where('trade_id', $trade->id)
            ->where('type', $request->type)
            ->where('index', $request->index)
            ->where('is_published', false)
            ->first();

        if (!$form) {
            $form = TradeForm::create([
                'trade_id' => $trade->id,
                'type' => $request->type,
                'index' => $request->index,
                'is_published' => false,
            ]);
        }

        return response()->json([
            'success' => true,
            'trade_id' => $trade->id,
            'form_key' => $form->id,
            'index' => $form->index,
            'is_published' => $form->is_published,
        ], 200);
    }
    public function getDraftTrades(Request $request): JsonResponse
    {
        $user = Auth::user();

        $trades = Trade::where('user_id', $user->id)
            ->where('is_published', false)
            ->get()
            ->map(function ($trade) {
                return [
                    'id' => $trade->id,
                    'draft_title' => $trade->draft_title,
                    'created_at' => $trade->created_at->toIso8601String(),
                    'updated_at' => $trade->updated_at->toIso8601String(),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $trades,
        ], 200);
    }
    public function getTradeData(Request $request, $tradeId): JsonResponse
    {
        $user = Auth::user();

        $trade = Trade::where('id', $tradeId)
            ->where('user_id', $user->id)
            ->first();

        if (!$trade) {
            return response()->json([
                'success' => false,
                'message' => 'Trade not found or unauthorized',
            ], 404);
        }

        $forms = TradeForm::where('trade_id', $tradeId)
            ->with(['sections' => function ($query) {
                $query->select('trade_form_id', 'section', 'data');
            }])
            ->get();

        return response()->json([
            'success' => true,
            'trade' => [
                'trade_id' => $trade->id,
                'draft_title' => $trade->draft_title,
                'is_published' => $trade->is_published,
            ],
            'forms' => $forms->map(function ($form) {
                return [
                    'form_key' => $form->id,
                    'type' => $form->type,
                    'index' => $form->index,
                    'is_published' => $form->is_published,
                    'sections' => $form->sections->map(function ($section) {
                        return [
                            'section' => $section->section,
                            'data' => $section->data,
                        ];
                    }),
                ];
            }),
        ], 200);
    }
    public function saveNotes(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'formKey' => 'required',
            'notes' => 'nullable|string|max:500',
            'field' => 'required|string|in:transaction_comments',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $form = TradeForm::where('id', $request->formKey)
            ->whereHas('trade', fn ($query) => $query->where('user_id', $user->id))
            ->first();

        if (!$form) {
            return response()->json([
                'success' => false,
                'message' => 'Trade form not found or unauthorized',
            ], 404);
        }

        $form->update(['notes' => $request->notes]);

        return response()->json([
            'success' => true,
            'message' => 'Notes saved successfully',
            'data' => ['notes' => $request->notes],
        ], 200);
    }

    public function saveSection(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'formKey' => 'required',
            'section' => 'required|string|in:overview,projection,outcome',
            'data' => 'required|array',
            'data.*.input' => 'required|string',
            'data.*.value' => 'nullable',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $form = TradeForm::where('id', $request->formKey)
            ->whereHas('trade', fn ($query) => $query->where('user_id', $user->id))
            ->first();

        if (!$form || $form->is_published) {
            return response()->json([
                'success' => false,
                'message' => 'Trade form not found, unauthorized, or already published',
            ], 404);
        }

        $section = TradeFormSection::updateOrCreate(
            [
                'trade_form_id' => $form->id,
                'section' => $request->section,
            ],
            [
                'data' => array_filter($request->data, fn ($item) => !isset($item['isFormula']) || !$item['isFormula']),
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Section data saved successfully',
            'data' => $section,
        ], 200);
    }
    public function publishForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'formKey' => 'required',
            'data' => 'required|array',
            'data.overview' => 'required|array',
            'data.projection' => 'required|array',
            'data.outcome' => 'required|array',
            'data.notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $form = TradeForm::where('id', $request->formKey)
            ->whereHas('trade', fn ($query) => $query->where('user_id', $user->id))
            ->first();

        if (!$form || $form->is_published) {
            return response()->json([
                'success' => false,
                'message' => 'Trade form not found, unauthorized, or already published',
            ], 404);
        }

        foreach (['overview', 'projection', 'outcome'] as $section) {
            TradeFormSection::updateOrCreate(
                [
                    'trade_form_id' => $form->id,
                    'section' => $section,
                ],
                ['data' => $request->data[$section]]
            );
        }

        $form->update([
            'is_published' => true,
            'notes' => $request->data['notes'],
        ]);

        if ($form->trade->is_published) {
            $this->createSnapshot($form);
        }

        return response()->json([
            'success' => true,
            'message' => 'Form published successfully',
            'data' => $form,
        ], 200);
    }

    public function publishTrade(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tradeId' => 'required|integer|exists:trades,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $trade = Trade::where('id', $request->tradeId)
            ->where('user_id', $user->id)
            ->first();

        if (!$trade || $trade->is_published) {
            return response()->json([
                'success' => false,
                'message' => 'Trade not found, unauthorized, or already published',
            ], 404);
        }

        $trade->update(['is_published' => true]);

        $trade->forms()->where('is_published', true)->get()->each(function ($form) {
            $this->createSnapshot($form);
        });

        return response()->json([
            'success' => true,
            'message' => 'Trade published successfully',
            'data' => $trade,
        ], 200);
    }

    protected function createSnapshot(TradeForm $form)
    {
        $sections = $form->sections()->get()->pluck('data', 'section')->toArray();
        $snapshotData = [
            'overview' => $sections['overview'] ?? [],
            'projection' => $sections['projection'] ?? [],
            'outcome' => $sections['outcome'] ?? [],
            'notes' => $form->notes,
        ];

        TradeSnapshot::create([
            'trade_id' => $form->trade_id,
            'trade_form_id' => $form->id,
            'data' => $snapshotData,
        ]);
    }
    public function getFormStatus(Request $request, $formKey)
    {
        $form = TradeForm::where('id', $formKey)
            ->whereHas('trade', fn ($query) => $query->where('user_id', Auth::id()))
            ->first();

        if (!$form) {
            return response()->json([
                'success' => false,
                'message' => 'Trade form not found or unauthorized',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'is_published' => $form->is_published,
        ], 200);
    }
    public function deleteForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'formKey' => 'required|exists:trade_forms,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $form = TradeForm::where('id', $request->formKey)
            ->whereHas('trade', fn ($query) => $query->where('user_id', $user->id))
            ->where('is_published', false)
            ->first();

        if (!$form) {
            return response()->json([
                'success' => false,
                'message' => 'Form not found, unauthorized, or already published',
            ], 404);
        }

        $form->delete();

        return response()->json([
            'success' => true,
            'message' => 'Form deleted successfully',
        ], 200);
    }

    public function calculateFormulas(Request $request)
    {
        $inputs            = $request->input('inputs', []);
        $locked            = $request->input('locked', []);
        $formulaModeInputs = $request->input('formulaModeInputs', []);

        $lockedFields = array_map('strtoupper', array_keys(array_filter($locked, fn ($value) => $value === true)));
        $changedField = strtoupper($request->input('changedField', ''));

        $inputs = collect($inputs)
            ->mapWithKeys(fn ($v, $k) => [strtoupper($k) => $v])
            ->toArray();

        $formulaModeInputs = collect($formulaModeInputs)
            ->mapWithKeys(fn ($v, $k) => [strtoupper($k) => $v])
            ->toArray();

        $jsonFile    = public_path('TradeReply_Formulas.json');
        $formulaData = json_decode(file_get_contents($jsonFile), true) ?? [];

        $updatedFields  = [];
        $iterationCount = 0;
        $maxIterations  = 15;

        do {
            $newUpdates = [];

            foreach ($formulaData as $formulaItem) {
                foreach (['TRADE', 'TRANSACTION', 'PORTFOLIO'] as $scopeKey) {
                    $scope = $formulaItem['SCOPES'][$scopeKey] ?? null;
                    if (!$scope) {
                        continue;
                    }

                    $targetField = strtoupper($scope['DATABASE FIELD'] ?? '');
                    $formula     = $scope['FORMULA'] ?? null;
                    if (!$targetField || !$formula) {
                        continue;
                    }

                    $dependentFields = $this->extractFormulaDependencies($formula);

                    if (in_array($targetField, $lockedFields, true)) {
                        continue;
                    }

                    if (isset($formulaModeInputs[$targetField]) && !$formulaModeInputs[$targetField]) {
                        continue;
                    }

                    if (
                        in_array($changedField, $dependentFields, true) ||
                        array_intersect(array_keys($updatedFields), $dependentFields)
                    ) {
                        $calculated = $this->evaluateFallbackFormula($formula, $inputs);
                        if (!is_null($calculated)) {
                            $newUpdates[$targetField] = $calculated;
                            $inputs[$targetField]     = $calculated;
                        }
                    }
                }
            }

            $updatedFields = array_merge($updatedFields, $newUpdates);

            $iterationCount++;
        } while (!empty($newUpdates) && $iterationCount < $maxIterations);

        return response()->json([
            'success'    => true,
            'calculated' => $updatedFields,
        ]);
    }

    private function extractFormulaDependencies(array $formula): array
    {
        $dependencies = [];

        for ($i = 1; $i <= 5; $i++) {
            $flag  = $formula["f{$i}"] ?? 0;
            $value = $formula["f{$i}v"] ?? null;

            if ($flag && $value) {
                $dependencies = array_merge($dependencies, $this->scanFieldsFromFormula($value));
            }
        }

        return array_unique(array_map('strtoupper', array_filter($dependencies, 'is_string')));
    }

    private function scanFieldsFromFormula($node): array
    {
        $fields = [];

        if (is_string($node)) {
            $fields[] = $node;
        }

        if (is_array($node)) {
            if (!empty($node['field'])) {
                $fields[] = $node['field'];
            }

            if (isset($node['condition']) && is_string($node['condition'])) {
                $fields[] = $node['condition'];
            }

            if (isset($node['value']) && is_string($node['value'])) {
                $fields[] = $node['value'];
            }

            if (!empty($node['condition']['field'])) {
                $fields[] = $node['condition']['field'];
            }

            if (!empty($node['fields']) && is_array($node['fields'])) {
                foreach ($node['fields'] as $child) {
                    $fields = array_merge($fields, $this->scanFieldsFromFormula($child));
                }
            }

            if (!empty($node['true_case'])) {
                $fields = array_merge($fields, $this->scanFieldsFromFormula($node['true_case']));
            }

            if (!empty($node['false_case'])) {
                $fields = array_merge($fields, $this->scanFieldsFromFormula($node['false_case']));
            }
        }

        return $fields;
    }

    private function evaluateFallbackFormula(array $formula, array $inputs)
    {
        for ($i = 1; $i <= 5; $i++) {
            if (($formula["f$i"] ?? 0) == 1 && isset($formula["f{$i}v"])) {
                $dependencies = $this->extractFormulaDependencies($formula["f{$i}v"]);

                $hasAllDependencies = true;
                foreach ($dependencies as $depField) {
                    $upperDep = strtoupper($depField);
                    if (!isset($inputs[$upperDep]) || $inputs[$upperDep] === '' || $inputs[$upperDep] === null) {
                        $hasAllDependencies = false;
                        break;
                    }
                }

                if ($hasAllDependencies) {
                    return $this->evaluateFormula($formula["f{$i}v"], $inputs);
                }
            }
        }
        return null;
    }

    private function evaluateFormula(array|string $formula, array $inputs)
    {
        if (is_string($formula)) {
            $upper = strtoupper($formula);
            return array_key_exists($upper, $inputs) ? $inputs[$upper] : $formula;
        }

        $op     = strtoupper($formula['operation'] ?? '');
        $fields = $formula['fields'] ?? [];

        switch ($op) {
            case 'ADD':
                $values = array_map(function ($f) use ($inputs) {
                    $val = $this->evaluateFormula($f, $inputs);
                    return is_numeric($val) ? floatval($val) : 0;
                }, $fields);

                return array_sum($values);

            case 'SUBTRACT':
                $a = $this->evaluateFormula($fields[0], $inputs);
                $b = $this->evaluateFormula($fields[1], $inputs);
                return (is_numeric($a) ? floatval($a) : 0) - (is_numeric($b) ? floatval($b) : 0);

            case 'MULTIPLY':
                return array_product(array_map(function ($f) use ($inputs) {
                    $val = $this->evaluateFormula($f, $inputs);
                    return is_numeric($val) ? floatval($val) : 1;
                }, $fields));

            case 'DIVIDE':
                $numerator   = $this->evaluateFormula($fields[0], $inputs);
                $denominator = $this->evaluateFormula($fields[1], $inputs);
                $num         = is_numeric($numerator) ? (float)$numerator : 0;
                $den         = is_numeric($denominator) ? (float)$denominator : 0;
                return $den == 0 ? 0 : $num / $den;

            case 'AVERAGE':
                $values        = array_map(fn ($f) => $this->evaluateFormula($f, $inputs), $fields);
                $numericValues = array_filter($values, fn ($v) => is_numeric($v));
                return count($numericValues) ? array_sum($numericValues) / count($numericValues) : null;

            case 'ABS':
                return abs($this->evaluateFormula($fields[0], $inputs));

            case 'REFERENCE':
                $field = strtoupper($formula['field'] ?? '');
                return $inputs[$field] ?? null;

            case 'WHEN_SET':
                $conditionKey = strtoupper($formula['condition'] ?? '');
                $valueKey     = strtoupper($formula['value'] ?? '');
                if (!empty($inputs[$conditionKey])) {
                    return $inputs[$valueKey] ?? null;
                }
                return null;

            case 'IF':
                $cond              = $formula['condition'];
                $evaluateCondition = function ($cond) use (&$evaluateCondition, $inputs) {
                    $op = strtoupper($cond['operation'] ?? '');

                    if ($op === 'OR' || $op === 'AND') {
                        $results = array_map(fn ($c) => $evaluateCondition($c), $cond['conditions'] ?? []);
                        return $op === 'OR' ? in_array(true, $results, true) : !in_array(false, $results, true);
                    }

                    $field = isset($cond['field']) && is_string($cond['field']) ? strtoupper($cond['field']) : '';
                    $val   = $inputs[$field] ?? null;

                    return match ($op) {
                        '=', '==' => $val == $cond['value'],
                        '!=', '<>' => $val != $cond['value'],
                        '>' => $val > $cond['value'],
                        '>=' => $val >= $cond['value'],
                        '<' => $val < $cond['value'],
                        '<=' => $val <= $cond['value'],
                        'IN' => in_array($val, $cond['values'] ?? []),
                        'IS_NULL' => is_null($val) || $val === '',
                        default => false,
                    };
                };

                $match = $evaluateCondition($cond);
                return $this->evaluateFormula($match ? $formula['true_case'] : $formula['false_case'], $inputs);

            case 'DATE_FORMAT':
                $field     = strtoupper($formula['field']);
                $timestamp = strtotime($inputs[$field] ?? '');
                return $timestamp ? date($formula['format'] ?? 'Y-m-d', $timestamp) : null;

            case 'YEAR':
                return date('Y', strtotime($inputs[strtoupper($formula['field'])] ?? ''));

            case 'WEEK':
                return date('W', strtotime($inputs[strtoupper($formula['field'])] ?? ''));

            case 'TIMESTAMPDIFF':
                $unit  = $formula['unit'] ?? 'day';
                $start = strtotime($this->evaluateFormula($formula['field1'], $inputs));
                $end   = strtotime($this->evaluateFormula($formula['field2'], $inputs));
                if (!$start || !$end) {
                    return null;
                }
                return match ($unit) {
                    'hour' => round(($end - $start) / 3600),
                    'day' => round(($end - $start) / 86400),
                    'month' => date('n', $end) - date('n', $start) + 12 * (date('Y', $end) - date('Y', $start)),
                    'year' => date('Y', $end) - date('Y', $start),
                    default => null,
                };

            default:
                return null;
        }
    }

    /**
     * Fetch all dynamic fields for Transaction, Trade, and Portfolio.
     */
    public function fetchAllFields(): JsonResponse
    {
        $formulas = $this->tradeBuilderService->getTradeReplayFormulas();

        $tradeFields       = [];
        $portfolioFields   = [];
        $transactionFields = [];

        foreach ($formulas as $item) {
            $scopes = $item['SCOPES'] ?? [];

            if (isset($scopes['TRANSACTION']['DATABASE FIELD'])) {
                $transactionFields[] = $scopes['TRANSACTION']['DATABASE FIELD'];
            }

            if (isset($scopes['TRADE']['DATABASE FIELD'])) {
                $tradeFields[] = $scopes['TRADE']['DATABASE FIELD'];
            }

            if (isset($scopes['PORTFOLIO']['DATABASE FIELD'])) {
                $portfolioFields[] = $scopes['PORTFOLIO']['DATABASE FIELD'];
            }
        }

        $transactions = TransactionFieldDefinition::query()
            ->join('field_definitions', 'transaction_field_definitions.field_definition_id', '=', 'field_definitions.id')
            ->whereIn('transaction_field_definitions.database_field', $transactionFields)
            ->whereNotIn('transaction_field_definitions.database_field', ['transaction_manual_deposit', 'transaction_manual_deposit_type', 'transaction_withdrawal'])
            ->get([
                'field_definitions.field_name',
                'field_definitions.metric_dimension',
                'field_definitions.expected_values',
                'transaction_field_definitions.database_field',
                'transaction_field_definitions.summary',
                'transaction_field_definitions.account_field',
                'transaction_field_definitions.account_field_value'
            ])
            ->map(function ($item) use ($formulas) {
                $arr                    = $item->toArray();
                $arr['expected_values'] = !empty($arr['expected_values'])
                    ? array_map('trim', explode(',', $arr['expected_values']))
                    : [];
                return $this->tradeBuilderService->applyIsEditable($arr, $formulas);
            })
            ->toArray();

        $trades = TradeFieldDefinition::query()
            ->join('field_definitions', 'trade_field_definitions.field_definition_id', '=', 'field_definitions.id')
            ->whereIn('trade_field_definitions.database_field', $tradeFields)
            ->whereNotIn('trade_field_definitions.database_field', ['trade_manual_deposit', 'trade_manual_deposit_type', 'trade_withdrawal'])
            ->get([
                'field_definitions.field_name',
                'field_definitions.metric_dimension',
                'field_definitions.expected_values',
                'trade_field_definitions.database_field',
                'trade_field_definitions.summary',
                'trade_field_definitions.id as id',
            ])
            ->map(function ($item) use ($formulas) {
                $arr                    = $item->toArray();
                $arr['expected_values'] = !empty($arr['expected_values'])
                    ? array_map('trim', explode(',', $arr['expected_values']))
                    : [];
                return $this->tradeBuilderService->applyIsEditable($arr, $formulas);
            })
            ->toArray();

        $portfolios = PortfolioFieldDefinition::query()
            ->with(['userFieldValue' => function ($query) {
                $query->where('user_id', auth()->id());
            }])
            ->join('field_definitions', 'portfolio_field_definitions.field_definition_id', '=', 'field_definitions.id')
            ->whereIn('portfolio_field_definitions.database_field', $portfolioFields)
            ->whereNotIn('portfolio_field_definitions.database_field', ['portfolio_manual_deposit', 'portfolio_manual_deposit_type', 'portfolio_withdrawal'])
            ->get([
                'field_definitions.field_name',
                'field_definitions.metric_dimension',
                'field_definitions.expected_values',
                'portfolio_field_definitions.database_field',
                'portfolio_field_definitions.summary',
                'portfolio_field_definitions.account_field',
                'portfolio_field_definitions.account_field_value',
                'portfolio_field_definitions.id as id',
            ])
            ->map(function ($item) use ($formulas) {
                $arr                    = $item->toArray();
                $arr['portfolioValue']  = $item->userFieldValue->value ?? null;
                $arr['expected_values'] = !empty($arr['expected_values'])
                    ? array_map('trim', explode(',', $arr['expected_values']))
                    : [];
                return $this->tradeBuilderService->applyIsEditable($arr, $formulas);
            })
            ->toArray();

        return response()->json([
            'transactions' => $transactions,
            'trades'       => $trades,
            'portfolios'   => $portfolios
        ]);
    }
}
