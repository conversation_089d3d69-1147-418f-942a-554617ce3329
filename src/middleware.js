import { NextResponse } from 'next/server';
import {
  SECURITY_PROTECTED_ROUTES,
  AUTH_FLOW_PROTECTED_ROUTES,
  VALID_REFERRER_PREFIXES,
  INVALID_REFERRER_PATHS,
  VALID_SECURITY_CHECK_REFERRERS,
  VALID_AUTH_FLOW_REFERRERS,
  isValidSecureRoute,
  isValidAuthFlowRoute,
  getDirectAccessFallbackUrl,
  getAuthFlowFallbackUrl
} from '@/config/securityRoutes';

/**
 * Validate security verification cookie
 */
function isValidSecurityCookie(cookieValue) {
  if (!cookieValue) {
    return false;
  }

  // Simple validation for 'true' value (legacy)
  if (cookieValue === 'true') {
    return true;
  }

  // Simple validation for 'verified' value (fallback)
  if (cookieValue === 'verified') {
    return true;
  }

  // Check if it's a session token (64 character hex string)
  if (typeof cookieValue === 'string' && cookieValue.length === 64 && /^[a-f0-9]+$/i.test(cookieValue)) {
    // For session tokens, we assume they're valid on client side
    // Backend will do the authoritative validation
    return true;
  }

  // Check if it's a UUID format (with hyphens)
  if (typeof cookieValue === 'string' && /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(cookieValue)) {
    // For UUID session tokens, we assume they're valid on client side
    // Backend will do the authoritative validation
    return true;
  }

  // Validate old encrypted payload format for backward compatibility
  try {
    const payload = JSON.parse(atob(cookieValue));

    if (payload.verified_at) {
      const verifiedAt = new Date(payload.verified_at);
      const now = new Date();
      const diffInSeconds = (now.getTime() - verifiedAt.getTime()) / 1000;

      // Check if within reasonable window (backend will do authoritative validation)
      // Use 15 minutes as a safe buffer since backend config may vary
      return diffInSeconds <= 900;
    }
  } catch (e) {
    // Invalid cookie format
    return false;
  }

  return false;
}

export function middleware(req) {
  const token = req.cookies.get('authToken');
  const authFlow = req.cookies.get('auth_flow_verified');
  const pathname = req.nextUrl.pathname;
  const ref = req.nextUrl.searchParams.get('ref');
  const sessionId = req.nextUrl.searchParams.get('session_id');

  const activeSubscription = req.cookies.get('active_subscription');
  const isFreeUser = !activeSubscription || activeSubscription?.plan?.billing_type === 'free';
  const isLoggedIn = !!token;

  const firstLogin = req.cookies.get('first_login')?.value || req.cookies.get('first_login');
  const returningLogin = req.cookies.get('returning_login')?.value || req.cookies.get('returning_login');
  const clickPaidButton = req.cookies.get('click_paid_button')?.value || req.cookies.get('click_paid_button');

  const authRoutes = ['/login', '/signup', '/forget-password', '/verify-email', '/home', '/create-username'];
  const protectedPrefixes = ['/user', '/account', '/dashboard', '/super-admin', '/not-found'];
  const flowProtectedRoutes = ['/change-password'];

  // ✅ Security verification protected pages (from centralized configuration)
  const securityProtectedRoutes = SECURITY_PROTECTED_ROUTES;

  // Helper function to validate if a path is a valid secure route (from centralized configuration)
  // Note: Using imported function from securityRoutes config

  // Helper function to check if user arrived via legitimate navigation (not direct URL access)
  const hasValidNavigationReferrer = (request) => {
    const referrer = request.headers.get('referer');
    const currentPath = request.nextUrl.pathname;

    // No referrer indicates direct URL access (typed in browser, bookmark, etc.)
    if (!referrer) {
      return false;
    }

    // Special handling for security-check page access
    if (currentPath.startsWith('/security-check')) {
      // For security-check page, only allow access from secure pages or middleware redirects
      try {
        const referrerUrl = new URL(referrer);
        const referrerPath = referrerUrl.pathname;

        // ✅ Special case: Allow access from signup page when signup parameter is present
        const isSignupFlow = req.nextUrl.searchParams.has('signup') && referrerPath === '/signup';

        // Allow access from secure pages (when middleware redirects users to security-check)
        const isFromSecurePage = securityProtectedRoutes.some(route => referrerPath.startsWith(route));

        // Allow access from valid application pages that might trigger security verification
        const validSecurityCheckReferrers = VALID_SECURITY_CHECK_REFERRERS;
        const isFromValidPage = validSecurityCheckReferrers.some(prefix => referrerPath.startsWith(prefix));

        console.log('Security-check referrer validation:', {
          referrerPath: referrerPath,
          isFromSecurePage: isFromSecurePage,
          isFromValidPage: isFromValidPage,
          isSignupFlow: isSignupFlow
        });

        return isFromSecurePage || isFromValidPage || isSignupFlow;
      } catch (error) {
        console.warn('Error parsing referrer URL for security-check:', error);
        return false;
      }
    }

    // Special case: Allow referrers from security-check page (after successful verification)
    try {
      const referrerUrl = new URL(referrer);
      if (referrerUrl.pathname.startsWith('/security-check')) {
        console.log('MIDDLEWARE: Allowing navigation from security-check page:', {
          referrer: referrer,
          referrerPath: referrerUrl.pathname,
          currentPath: currentPath
        });
        return true; // Allow navigation from security-check page
      }
    } catch (error) {
      console.warn('MIDDLEWARE: Error parsing referrer URL:', error);
      // Continue with normal validation if URL parsing fails
    }

    // Validate that referrer is from a valid application page
    return isValidApplicationReferrer(referrer, request);
  };

  // Helper function to check if the referrer is from a valid application page
  const isValidApplicationReferrer = (referrer, request) => {
    try {
      const parsedUrl = new URL(referrer);

      // Domain comparison logic removed to ensure consistent behavior across environments
      
      // Get the referrer path
      const referrerPath = parsedUrl.pathname;

      // Don't allow referrers from invalid pages (from centralized configuration)
      // Note: /security-check is handled specially in hasValidNavigationReferrer
      if (INVALID_REFERRER_PATHS.some(invalidPage => referrerPath.startsWith(invalidPage))) {
        return false;
      }

      // Allow referrers from valid application pages (from centralized configuration)
      return VALID_REFERRER_PREFIXES.some(validPrefix => referrerPath.startsWith(validPrefix));
    } catch (error) {
      console.warn('Error validating referrer:', error);
      return false;
    }
  };

  // Helper function to validate auth flow referrers
  const hasValidAuthFlowReferrer = (req) => {
    const referrer = req.headers.get('referer');
    const currentPath = req.nextUrl.pathname;

    if (!referrer) {
      console.log('Auth flow referrer validation: No referrer found');
      return false;
    }

    try {
      const referrerUrl = new URL(referrer);
      const referrerPath = referrerUrl.pathname;

      console.log('Auth flow referrer validation:', {
        currentPath: currentPath,
        referrer: referrer,
        referrerPath: referrerPath
      });

      // Allow access from valid auth flow referrer pages
      const isFromValidAuthFlowPage = VALID_AUTH_FLOW_REFERRERS.some(prefix => referrerPath.startsWith(prefix));

      console.log('Auth flow referrer validation result:', {
        referrerPath: referrerPath,
        isFromValidAuthFlowPage: isFromValidAuthFlowPage,
        validReferrers: VALID_AUTH_FLOW_REFERRERS
      });

      return isFromValidAuthFlowPage;
    } catch (error) {
      console.warn('Error parsing referrer URL for auth flow validation:', error);
      return false;
    }
  };

  // Helper function to get appropriate fallback URL for direct access prevention (from centralized configuration)
  // Note: Using imported function from securityRoutes config

  if (clickPaidButton && isLoggedIn) {
    const response = NextResponse.redirect(new URL('/checkout', req.url));
    response.cookies.set('click_paid_button', '', { maxAge: -1 });
    return response;
  }

  // ✅ Handle security-check page access early (before other auth checks)
  // Allow both logged-in and non-logged-in users to access security-check
  if (pathname.startsWith('/security-check')) {
    // Skip other authentication checks and proceed to security-check specific logic below
    // This allows the page to handle both signup flow (non-logged-in) and account security (logged-in)
  } else {
    // Apply normal authentication checks for other routes
    if (authRoutes.some((route) => pathname.startsWith(route))) {
      if (isLoggedIn) {
        return NextResponse.redirect(new URL('/user/dashboard', req.url));
      }
    }

    if (flowProtectedRoutes.includes(pathname)) {
      if (!isLoggedIn && !authFlow) {
        return NextResponse.redirect(new URL('/login', req.url));
      }
    }

    if (protectedPrefixes.some((prefix) => pathname.startsWith(prefix))) {
      if (!isLoggedIn) {
        return NextResponse.redirect(new URL('/login', req.url));
      }
    }
  }

  // ✅ Handle direct access prevention for security-check page
  if (pathname.startsWith('/security-check')) {
    // Check for direct URL access to security-check page
    const hasValidReferrer = hasValidNavigationReferrer(req);

    console.log('Security-check page access validation:', {
      pathname: pathname,
      referrer: req.headers.get('referer') || 'none',
      hasValidReferrer: hasValidReferrer
    });

    if (!hasValidReferrer) {
      // Direct access to security-check detected - redirect to safe fallback
      // Use different fallback based on authentication status
      const fallbackUrl = isLoggedIn ? '/account/overview' : '/signup';

      console.log('Direct access to security-check detected - redirecting to fallback:', {
        attemptedPath: pathname,
        fallbackUrl: fallbackUrl,
        isLoggedIn: isLoggedIn,
        referrer: req.headers.get('referer') || 'none'
      });

      return NextResponse.redirect(new URL(fallbackUrl, req.url));
    }

    // Allow legitimate access to security-check page (proper referrer found)
    console.log('Legitimate access to security-check page allowed');
    return NextResponse.next();
  }

  // ✅ Handle referrer-based access control for auth flow protected routes
  if (AUTH_FLOW_PROTECTED_ROUTES.some((route) => pathname.startsWith(route))) {

    // Check for direct URL access (referrer-based access control)
    const hasValidAuthReferrer = hasValidAuthFlowReferrer(req);
    console.log('Auth flow referrer validation result:', {
      pathname: pathname,
      referrer: req.headers.get('referer') || 'none',
      hasValidAuthReferrer: hasValidAuthReferrer
    });

    if (!hasValidAuthReferrer) {
      const fallbackUrl = getAuthFlowFallbackUrl(pathname);

      console.log('Direct URL access to auth flow route detected - redirecting to fallback:', {
        attemptedPath: pathname,
        fallbackUrl: fallbackUrl,
        referrer: req.headers.get('referer') || 'none'
      });

      return NextResponse.redirect(new URL(fallbackUrl, req.url));
    }

    // Allow legitimate access to auth flow route (proper referrer found)
    console.log('Legitimate access to auth flow route allowed:', pathname);
    return NextResponse.next();
  }

  // ✅ Handle referrer-based access control and security verification for secure pages
  if (securityProtectedRoutes.some((route) => pathname.startsWith(route))) {

    // Check for direct URL access (referrer-based access control)
    const hasValidReferrer = hasValidNavigationReferrer(req);
    console.log('Referrer validation result:', {
      pathname: pathname,
      referrer: req.headers.get('referer') || 'none',
      hasValidReferrer: hasValidReferrer
    });

    if (!hasValidReferrer) {
      const fallbackUrl = getDirectAccessFallbackUrl(pathname);

      console.log('Direct URL access detected - redirecting to fallback:', {
        attemptedPath: pathname,
        fallbackUrl: fallbackUrl,
        referrer: req.headers.get('referer') || 'none'
      });

      return NextResponse.redirect(new URL(fallbackUrl, req.url));
    }

    // Continue with normal security verification for legitimate navigation
    const securityVerified = req.cookies.get('security_verified');

    console.log('MIDDLEWARE: Security verification check:', {
      pathname,
      cookieExists: !!securityVerified,
      cookieValue: securityVerified?.value,
      isValid: securityVerified ? isValidSecurityCookie(securityVerified.value) : false,
      referrer: req.headers.get('referer') || 'none'
    });

    if (!securityVerified || !isValidSecurityCookie(securityVerified.value)) {
      console.log('Redirecting to security-check from:', pathname);
      // Use pathname + search params instead of full URL to avoid domain issues
      const fullPath = pathname + req.nextUrl.search;

      // Validate that the path is a valid secure route before including it in next parameter
      if (isValidSecureRoute(fullPath)) {
        const nextUrl = encodeURIComponent(fullPath);
        console.log('Valid secure route, including next parameter:', fullPath);
        return NextResponse.redirect(new URL(`/security-check?next=${nextUrl}`, req.url));
      } else {
        console.warn('Invalid secure route detected, redirecting without next parameter:', fullPath);
        return NextResponse.redirect(new URL('/security-check', req.url));
      }
    }

    console.log('Security verification passed for:', pathname);
  }


  // ✅ Handle first login (signup)

  if (firstLogin && isLoggedIn) {
    const response = isFreeUser
      ? NextResponse.redirect(new URL('/pricing?source=signup_free_login_upgrade&feature=buy_trial', req.url))
      : NextResponse.redirect(new URL('/dashboard?source=signup_member_login_default', req.url));
    response.cookies.set('first_login', '', { maxAge: -1 });
    return response;
  }

  if (returningLogin && isLoggedIn) {
    const response = isFreeUser
      ? NextResponse.redirect(new URL('/pricing?source=free_login_upgrade&feature=buy_trial', req.url))
      : NextResponse.redirect(new URL('/dashboard?source=member_login_default', req.url));
    response.cookies.set('returning_login', '', { maxAge: -1 });
    return response;
  }

  if (pathname === '/pricing' && isLoggedIn) {
    if (sessionId) return NextResponse.next();

    const hasSource = req.nextUrl.searchParams.get('source');
    if (!hasSource) {
      let source = 'default_pricing';
      if (ref === 'header') source = 'header_menu_pricing';
      else if (ref === 'footer') source = 'footer_menu_pricing';
      else if (ref === 'header_upgrade') source = 'header_upgrade_button';

      const prefix = isFreeUser ? 'free' : 'member';
      const redirectUrl = `/pricing?source=${prefix}_${source}&feature=buy_trial`;

      return NextResponse.redirect(new URL(redirectUrl, req.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/admin/:path*',
    '/user/:path*',
    '/pricing',
    '/change-password',
    '/account/:path*',
    '/dashboard/:path*',
    '/super-admin/:path*',
    '/not-found',
    '/security-check',
  ],

};
