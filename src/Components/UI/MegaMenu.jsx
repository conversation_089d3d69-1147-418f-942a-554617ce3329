'use client';
import React, { useEffect, useState, useRef } from 'react';
import Link from 'next/link';
import { ChevronRight, ChevronDown } from 'lucide-react';

export default function MegaMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState(null);
  const [categories, setCategories] = useState([]);
  const menuRef = useRef(null);
  const timeoutRef = useRef(null);

  useEffect(() => {
    const fetchData = async () => {
      const res = await fetch('/tradereply-categories.json');
      const data = await res.json();
      setCategories(data.categories);
    };
    fetchData();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        clearTimeout(timeoutRef.current); // Clear any delayed close
        setIsOpen(false);
        setActiveCategory(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleMouseEnter = (cat) => {
    clearTimeout(timeoutRef.current);
    setActiveCategory(cat);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setActiveCategory(null);
    }, 150);
  };

  return (
    <div className="megaMenu" ref={menuRef}>
      <button onClick={() => setIsOpen(!isOpen)} className="megaMenu__toggle">
        Shop <ChevronDown size={18} />
      </button>

      {isOpen && (
        <div
          className={`megaMenu__dropdown ${activeCategory ? 'expanded' : ''}`}
          onMouseLeave={handleMouseLeave}
        >
          <div className="megaMenu__categories">
            {categories.map((cat) => (
              <div
                key={cat.slug}
                className={`megaMenu__category ${activeCategory?.slug === cat.slug ? 'active' : ''}`}
                onMouseEnter={() => handleMouseEnter(cat)}
              >
                <Link href="#" className="megaMenu__category-link">
                  <span>{cat.name}</span>
                  <ChevronRight className="megaMenu__icon" />
                </Link>
              </div>
            ))}
          </div>

          <div
            className={`megaMenu__subcategories ${activeCategory ? 'visible' : ''}`}
            onMouseEnter={() => clearTimeout(timeoutRef.current)}
          >
            {activeCategory && (
              <div className="megaMenu__columns">
                {activeCategory.subcategories.map((sub) => (
                  <div key={sub.slug} className="megaMenu__column">
                    <p className="megaMenu__subtitle">{sub.name}</p>
                    <ul className="megaMenu__items">
                      {sub.subcategories.map((item) => (
                        <li key={item.slug}>
                          <Link
                            href={`/marketplace/categories/${activeCategory.slug}/${sub.slug}/${item.slug}`}
                            className="megaMenu__link"
                          >
                            {item.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
